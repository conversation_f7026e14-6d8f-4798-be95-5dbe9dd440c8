import React, { useRef, useEffect, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Html, useBounds } from '@react-three/drei';
import { GLTFLoader, OBJLoader, FBXLoader } from 'three-stdlib';
import * as THREE from 'three';
import { mightHaveCorsIssues } from '../../shared/utils';

// Error indicator component
export function ErrorIndicator({ error }: { error: string }) {
  return (
    <Html center>
      <div className="tailwind-text-center tailwind-p-4 tailwind-bg-red-50 tailwind-rounded-lg tailwind-shadow tailwind-max-w-md">
        <p className="tailwind-text-red-600 tailwind-font-medium">
          Không thể tải mô hình 3D
        </p>
        <p className="tailwind-text-gray-700 tailwind-text-sm tailwind-mt-1">
          {error}
        </p>
        <div className="tailwind-mt-3 tailwind-text-xs tailwind-text-gray-500 tailwind-text-left">
          <p className="tailwind-font-medium tailwind-mb-1">
            Ki<PERSON>m tra các vấn đề sau:
          </p>
          <ul className="tailwind-list-disc tailwind-pl-4">
            <li>URL của mô hình có chính xác không?</li>
            <li>Định dạng tệp có được hỗ trợ không? (.obj, .glb, .fbx)</li>
            <li>Tệp có thể truy cập được không? (CORS, quyền truy cập)</li>
            <li>Tệp có bị hỏng hoặc không đúng định dạng không?</li>
          </ul>
        </div>
      </div>
    </Html>
  );
}

// Model component props
export interface Model3DRendererProps {
  url: string;
  format: 'glb' | 'obj' | 'fbx';
  autoRotate: boolean;
  rotationSpeed: number;
  onLoad?: () => void;
  onError?: (error: any) => void;
  onProgress?: (progress: number) => void;
}

// Helper function to create a standard material
const createStandardMaterial = (color = 0xcccccc, map?: THREE.Texture) => {
  return new THREE.MeshStandardMaterial({
    color,
    map,
    roughness: 0.7,
    metalness: 0.2,
    side: THREE.DoubleSide,
  });
};

// Helper function to process materials
const processMaterial = (material: THREE.Material | null) => {
  if (!material) {
    return createStandardMaterial();
  }

  material.needsUpdate = true;

  // Convert basic/phong materials to standard for better lighting
  if (
    material instanceof THREE.MeshBasicMaterial ||
    material instanceof THREE.MeshPhongMaterial
  ) {
    return createStandardMaterial(
      material.color.getHex(),
      material.map || undefined
    );
  }

  // Adjust standard material properties
  if (
    material instanceof THREE.MeshStandardMaterial ||
    material instanceof THREE.MeshPhysicalMaterial
  ) {
    if (material.roughness === 0) material.roughness = 0.5;
    if (material.metalness === 0) material.metalness = 0.2;
    material.side = THREE.DoubleSide;
  }

  return material;
};

// Model component
const Model3DRenderer: React.FC<Model3DRendererProps> = ({
  url,
  format,
  autoRotate,
  rotationSpeed,
  onLoad,
  onError,
  onProgress,
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const bounds = useBounds();
  const [model, setModel] = useState<THREE.Object3D | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);

  // Use refs to track current values of autoRotate and rotationSpeed
  // This allows us to access their current values in useFrame without dependencies
  const autoRotateRef = useRef(autoRotate);
  const rotationSpeedRef = useRef(rotationSpeed);

  // Update refs when props change
  useEffect(() => {
    autoRotateRef.current = autoRotate;
    rotationSpeedRef.current = rotationSpeed;
  }, [autoRotate, rotationSpeed]);

  // Cleanup blob URL when component unmounts or URL changes
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  // Handle model rotation using refs instead of direct props
  useFrame((_, delta) => {
    if (groupRef.current && autoRotateRef.current) {
      groupRef.current.rotation.y += delta * rotationSpeedRef.current * 0.5;
    }
  });

  // Helper function to process a loaded model
  const processLoadedModel = (model: THREE.Object3D) => {
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // Process materials
        if (!child.material) {
          child.material = createStandardMaterial();
        } else if (Array.isArray(child.material)) {
          child.material = child.material.map(processMaterial);
        } else {
          child.material = processMaterial(child.material);
        }
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    return model;
  };

  // Helper function to fetch and execute b64.js file to get base64 data
  const fetchBase64FromScript = async (scriptUrl: string): Promise<string> => {
    try {
      const response = await fetch(scriptUrl);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch script: ${response.status} ${response.statusText}`
        );
      }

      const scriptContent = await response.text();

      // Create a function to execute the script and capture the base64 data
      // The script should define a variable or return the base64 string
      const executeScript = new Function(scriptContent);
      const jsData = executeScript();
      const base64Data = jsData.base64;
      if (!base64Data || typeof base64Data !== 'string') {
        throw new Error('Script did not return valid base64 data');
      }

      return base64Data;
    } catch (error) {
      throw new Error(
        `Failed to load base64 from script: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  };

  // Helper function to convert base64 to blob URL
  const base64ToBlobUrl = (base64Data: string, mimeType: string): string => {
    try {
      // Remove data URL prefix if present
      const base64String = base64Data.replace(/^data:[^;]+;base64,/, '');

      // Decode Base64 string
      const byteCharacters = atob(base64String.split(',')[1] || base64String);
      const byteNumbers = new Array(byteCharacters.length);

      // Convert to byte array
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }

      // Create Blob and URL
      const byteArray = new Uint8Array(byteNumbers);

      // Create blob and return URL
      const blob = new Blob([byteArray], { type: mimeType });
      return URL.createObjectURL(blob);
    } catch (error) {
      throw new Error(
        `Failed to convert base64 to blob URL: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  };

  // Helper function to get MIME type based on format
  const getMimeType = (format: string): string => {
    switch (format.toLowerCase()) {
      case 'glb':
        return 'model/gltf-binary';
      case 'obj':
        return 'text/plain';
      case 'fbx':
        return 'application/octet-stream';
      default:
        return 'application/octet-stream';
    }
  };

  // Helper function to load a model with a specific loader
  const loadModelWithLoader = async (
    loader: GLTFLoader | OBJLoader | FBXLoader,
    url: string
  ): Promise<THREE.Object3D> => {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Model loading timeout'));
      }, 30000); // Increased timeout for blob URL processing
      console.log('url', url);
      // Thêm một độ trễ nhỏ để đảm bảo tiến độ bắt đầu từ 0
      setTimeout(() => {
        loader.load(
          url,
          (result) => {
            clearTimeout(timeoutId);
            // Handle different loader result types
            if ('scene' in result) {
              resolve(result.scene); // GLTFLoader returns { scene }
            } else {
              resolve(result); // OBJLoader and FBXLoader return Object3D directly
            }
          },
          undefined, // Skip progress callback - we'll use LoadingManager instead
          (error) => {
            clearTimeout(timeoutId);
            reject(error);
          }
        );
      }, 100);
    });
  };
  // Load model based on format
  useEffect(() => {
    if (!url) return;

    // Reset state when loading a new model
    setLoading(true);
    setError(null);

    // Thông báo tiến độ ban đầu là 0%
    if (onProgress) {
      onProgress(0);
    }

    // Create a shared loading manager for all loaders
    const manager = new THREE.LoadingManager();

    // Biến để theo dõi tiến độ tải tối đa đã đạt được
    let maxProgress = 0;

    // Track loading progress
    manager.onProgress = (_, itemsLoaded, itemsTotal) => {
      // Tính toán tiến độ hiện tại
      const currentProgress = (itemsLoaded / itemsTotal) * 100;
      if (currentProgress > maxProgress) {
        maxProgress = currentProgress;
        if (onProgress) {
          onProgress(maxProgress);
        }
      }
    };

    // Handle loading complete
    manager.onLoad = () => {
      setTimeout(() => {
        if (onProgress) {
          onProgress(100);
        }
      }, 100);
    };

    // Handle loading error
    manager.onError = (url) => {
      setError(`Không thể tải tệp: ${url}`);
      setLoading(false);
      if (onError) {
        onError(new Error(`Không thể tải tệp: ${url}`));
      }
    };

    const loadModel = async () => {
      try {
        let loadedModel: THREE.Object3D | null = null;
        let modelUrl = url;
        // Check if URL points to a .b64.js file
        if (url.endsWith('.js')) {
          try {
            // Fetch and execute the b64.js script to get base64 data
            const base64Data = await fetchBase64FromScript(url);
            // Convert base64 to blob URL
            const mimeType = getMimeType(format);
            modelUrl = base64ToBlobUrl(base64Data, mimeType);
            // Store blob URL for cleanup
            setBlobUrl(modelUrl);

            // Update progress to show conversion completed
            if (onProgress) {
              onProgress(25); // 25% for script loading and conversion
            }
          } catch (conversionError) {
            throw new Error(
              `Failed to process b64.js file: ${
                conversionError instanceof Error
                  ? conversionError.message
                  : 'Unknown error'
              }`
            );
          }
        }

        // Load model based on format using the processed URL
        if (format === 'glb') {
          loadedModel = await loadModelWithLoader(
            new GLTFLoader(manager),
            modelUrl
          );
        } else if (format === 'obj') {
          loadedModel = await loadModelWithLoader(
            new OBJLoader(manager),
            modelUrl
          );
        } else if (format === 'fbx') {
          loadedModel = await loadModelWithLoader(
            new FBXLoader(manager),
            modelUrl
          );
        }

        if (loadedModel) {
          // Process the model (materials, shadows, etc.)
          processLoadedModel(loadedModel);

          // Set the model
          setModel(loadedModel);

          // Fit model to view
          if (groupRef.current) {
            // Wait for the next frame to ensure the model is in the scene
            setTimeout(() => {
              try {
                bounds.refresh().fit();
                setLoading(false);
                if (onLoad) onLoad();
              } catch (fitError) {
                // Ensure loading state is updated even if fitting fails
                setLoading(false);
                if (onLoad) onLoad();
              }
            }, 100);
          } else {
            setLoading(false);
            if (onLoad) onLoad();
          }
        }
      } catch (error) {
        // Provide more detailed error message
        let errorMessage = 'Could not load model';

        if (error instanceof Error) {
          errorMessage = `Loading error: ${error.message}`;
        }

        // Check for network errors
        if (
          error instanceof TypeError &&
          error.message.includes('Failed to fetch')
        ) {
          errorMessage =
            'Network error: Could not access the model file. Check the URL and ensure it is accessible.';
        }

        // Check for CORS errors
        if (error instanceof DOMException && error.name === 'SecurityError') {
          errorMessage =
            'CORS error: The model file is blocked by cross-origin policy. Ensure proper CORS headers are set.';
        }

        // Proactively check for potential CORS issues
        if (mightHaveCorsIssues(url)) {
          errorMessage =
            'Possible CORS issue detected: The model is hosted on a different domain. ' +
            'This may cause loading problems. Consider hosting the model on the same server ' +
            'or configuring CORS headers on the server hosting the model.';
        }

        setError(errorMessage);
        setLoading(false);
        if (onError) onError(error);
      }
    };

    // Start the model loading process
    loadModel();

    // Add a safety timeout to ensure loading indicator doesn't get stuck
    const safetyTimeoutId = setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 15000); // 15 seconds safety timeout

    // Clean up the safety timeout
    return () => clearTimeout(safetyTimeoutId);
  }, [url, bounds]);

  // Don't show loading indicator here, let parent component handle it
  if (loading) {
    return null;
  }

  // If error, show error message
  if (error) {
    return <ErrorIndicator error={error} />;
  }

  // If no model, show default cube
  if (!model) {
    return <></>;
  }

  // Normalize and process the model
  const normalizeModel = (inputModel: THREE.Object3D): THREE.Object3D => {
    // Clone the model to avoid modifying the original
    const processedModel = inputModel.clone();

    // Normalize the model scale and position
    const box = new THREE.Box3().setFromObject(processedModel);
    const size = box.getSize(new THREE.Vector3());
    const center = box.getCenter(new THREE.Vector3());

    // Calculate scale to normalize size (max dimension to 2 units)
    const maxDimension = Math.max(size.x, size.y, size.z);
    const scale = maxDimension > 0 ? 2 / maxDimension : 1;

    // Apply transformations to the model
    processedModel.scale.multiplyScalar(scale);
    processedModel.position.sub(center.multiplyScalar(scale));

    return processedModel;
  };

  // Process the model for consistent display
  const processedModel = normalizeModel(model);

  // Return the processed model
  return (
    <group ref={groupRef}>
      <primitive object={processedModel} />
    </group>
  );
};

export default Model3DRenderer;
